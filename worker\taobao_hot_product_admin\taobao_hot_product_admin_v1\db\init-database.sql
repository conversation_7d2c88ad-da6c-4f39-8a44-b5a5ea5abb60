


CREATE TABLE anchors (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    anchor_name TEXT NOT NULL,
    anchor_id TEXT UNIQUE NOT NULL,
    anchor_cookie TEXT,
    password TEXT,
    status TEXT DEFAULT 'active',
    total_orders INTEGER DEFAULT 0,
    total_amount REAL DEFAULT 0,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX idx_anchors_anchor_id ON anchors(anchor_id);
CREATE INDEX idx_anchors_created_at ON anchors(created_at);
CREATE INDEX idx_anchors_status ON anchors(status);




---------------------------------------------------------------------------------------------------
---------------------------------------------------------------------------------------------------

-- 产品表
CREATE TABLE products (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    anchor_name TEXT NOT NULL,                  
    product_id TEXT NOT NULL,                    
    product_title TEXT,                
    product_price DECIMAL(10,2)  NOT NULL,             
    commission_rate DECIMAL(5,2),               
    commission_amount DECIMAL(10,2),           
    product_source TEXT,                       
    alliance_channel TEXT,                      
    product_category TEXT,                       
    sales_365_days INTEGER DEFAULT 0,
    sales_30_days INTEGER DEFAULT 0,
    sales_7_days INTEGER DEFAULT 0,
    sales_7_days_growth_rate DECIMAL(5,2),
    orders_30_days INTEGER DEFAULT 0,
    anchor_sales_30_days DECIMAL(10,2) DEFAULT 0,
    tag INTEGER,
    date TEXT,
    product_type TEXT,   
    shop_name TEXT,                             -- 店铺名称
    safe_shop_id TEXT,                          -- 安全店铺ID
    feature_tags TEXT,
    batch_number TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 产品表索引
CREATE INDEX idx_products_anchor_name ON products(anchor_name);
CREATE INDEX idx_products_product_id ON products(product_id);
CREATE INDEX idx_products_product_source ON products(product_source);
CREATE INDEX idx_products_alliance_channel ON products(alliance_channel);
CREATE INDEX idx_products_batch_number ON products(batch_number);
CREATE INDEX idx_products_created_at ON products(created_at);
CREATE INDEX idx_products_sales_7_days_growth_rate ON products(sales_7_days_growth_rate);
CREATE INDEX idx_products_anchor_sales_30_days ON products(anchor_sales_30_days);
CREATE INDEX idx_products_shop_name ON products(shop_name);
CREATE INDEX idx_products_safe_shop_id ON products(safe_shop_id);

-- 复合索引
CREATE INDEX idx_products_anchor_batch ON products(anchor_name, batch_number);
CREATE UNIQUE INDEX idx_products_unique_anchor_product_batch ON products(anchor_name, product_id, batch_number);


-- 选品规则配置表
CREATE TABLE selection_rules (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    rule_name TEXT NOT NULL,                    -- 规则名称
    description TEXT,                           -- 规则描述
    total_target INTEGER DEFAULT 500,          -- 总目标数量
    rule_groups TEXT NOT NULL,                 -- 规则组配置(JSON格式)
    status TEXT DEFAULT 'active',              -- 状态: active, inactive
    created_by TEXT,                           -- 创建者
    last_used_at DATETIME,                     -- 最后使用时间
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 选品规则索引
CREATE INDEX idx_selection_rules_status ON selection_rules(status);
CREATE INDEX idx_selection_rules_created_at ON selection_rules(created_at);
CREATE INDEX idx_selection_rules_last_used_at ON selection_rules(last_used_at);

-- 选品执行记录表
CREATE TABLE selection_executions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    rule_id INTEGER NOT NULL,                  -- 关联的规则ID
    rule_name TEXT NOT NULL,                   -- 规则名称快照
    execution_time DATETIME NOT NULL,          -- 执行时间
    total_selected INTEGER DEFAULT 0,          -- 实际选中数量
    execution_result TEXT,                     -- 执行结果详情(JSON格式)
    batch_number TEXT,                         -- 生成的批次号
    status TEXT DEFAULT 'completed',           -- 执行状态: completed, failed, partial
    error_message TEXT,                        -- 错误信息
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (rule_id) REFERENCES selection_rules(id)
);

-- 选品执行记录索引
CREATE INDEX idx_selection_executions_rule_id ON selection_executions(rule_id);
CREATE INDEX idx_selection_executions_execution_time ON selection_executions(execution_time);
CREATE INDEX idx_selection_executions_batch_number ON selection_executions(batch_number);

-