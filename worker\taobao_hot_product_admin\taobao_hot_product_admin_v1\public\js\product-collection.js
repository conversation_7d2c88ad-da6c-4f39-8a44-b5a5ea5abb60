$(document).ready(function() {
    let collectionInterval = null;
    let isCollecting = false;
    let currentPageNum = 1;
    let collectedProducts = [];
    let selectedAnchor = null;
    let conditionData = null;

    // 初始化页面
    init();

    async function init() {
        try {
            // 加载筛选条件
            await loadConditions();
            // 加载主播列表
            await loadAnchors();
            // 绑定事件
            bindEvents();
        } catch (error) {
            console.error('初始化失败:', error);
            addLog('初始化失败: ' + error.message, 'error');
        }
    }

    // 加载筛选条件
    async function loadConditions() {
        try {
            // 基于condition.json实际结构的筛选条件数据
            conditionData = {
                "cateIds": [
                    {"name": "美容美妆", "value": "50010788,126762001"},
                    {"name": "个护清洁", "value": "50023282,1801,50016348,50023722,50025705"},
                    {"name": "母婴用品", "value": "50022517,50014812,201207402,35,50018004"},
                    {"name": "童装童鞋", "value": "50008165,122650005"},
                    {"name": "服饰", "value": "16,1625,50013864,50006843,30,50010404,50011740"},
                    {"name": "手表眼镜", "value": "50468001,28"},
                    {"name": "箱包皮具", "value": "50006842"},
                    {"name": "珠宝", "value": "50011397"},
                    {"name": "生鲜", "value": "50050359"},
                    {"name": "粮油副食", "value": "50016422"},
                    {"name": "零食饮品", "value": "124458005,50026316,50008141,50002766"},
                    {"name": "营养保健", "value": "50026800,50020275"},
                    {"name": "3C数码", "value": "50008090,50012164,11,124242008,50017300,50007218,20"},
                    {"name": "大家电", "value": "50022703"},
                    {"name": "生活电器", "value": "50012100,50002768,50012082,50011972"},
                    {"name": "家居百货", "value": "122852001,21,50008163,122928002,50020808,50025004,50020857,122950001"},
                    {"name": "厨房用具", "value": "122952001,50016349"},
                    {"name": "家装", "value": "50008164,27,126700003,50020332,50020485,50023804,50020611,50020579,124050001"},
                    {"name": "运动服鞋", "value": "50011699,50012029"},
                    {"name": "户外装备", "value": "50013886,122684003,50010728,50510002"},
                    {"name": "汽车", "value": "201162107,26,124354002,50074001"},
                    {"name": "玩具", "value": "25,124484008"},
                    {"name": "鲜花园艺", "value": "50007216"},
                    {"name": "宠物", "value": "29"},
                    {"name": "本地生活", "value": "50025111,50008075,50025110,201173506"},
                    {"name": "图画音像", "value": "33,34,23"},
                    {"name": "教育培训", "value": "50014927"},
                    {"name": "医疗健康", "value": "50023717,50023721"}
                ],
                "featured": [
                    {"name": "爆品", "value": "featuredPool=4;10796,featuredPool=4;10795,featuredPool=4;10794"},
                    {"name": "高佣", "value": "featured=30日高佣好货,featured=7日高佣好货,featured=90日高佣好货"},
                    {"name": "秒杀引流", "value": "featured=秒杀品"},
                    {"name": "稀缺", "value": "featured=稀缺品牌"},
                    {"name": "热销", "value": "featured=销量top单品,featured=成交top单品"},
                    {"name": "超级好价", "value": "starLevel=4,starLevel=5,compareResult=-1,realTimeCompareResult=-1"},
                    {"name": "新品", "value": "newItemTag=重磅新品,newItemTag=优质新品,newItemTag=天猫新品,newItemTag=淘宝新品"},
                    {"name": "高曝品", "value": "isHotItem=是"}
                ],
                "priceSelect": [
                    {"name": "1-100元", "value": "1_100"},
                    {"name": "100-300元", "value": "100_300"},
                    {"name": "300-600元", "value": "300_600"},
                    {"name": "600-1200元", "value": "600_1200"},
                    {"name": "1200-2000元", "value": "1200_2000"},
                    {"name": "2000元以上", "value": "2000_"}
                ],
                "commissionRateSelect": [
                    {"name": "1-5%", "value": "1_5"},
                    {"name": "5-10%", "value": "5_10"},
                    {"name": "10-20%", "value": "10_20"},
                    {"name": "20%以上", "value": "20_"}
                ],
                "soldQuantity365Select": [
                    {"name": "10-100件", "value": "10_100"},
                    {"name": "100-500件", "value": "100_500"},
                    {"name": "500-1000件", "value": "500_1000"},
                    {"name": "1000-5000件", "value": "1000_5000"},
                    {"name": "5000件以上", "value": "5000_"}
                ],
                "shopType": [
                    {"name": "天猫店", "value": "天猫店"},
                    {"name": "淘宝店", "value": "淘宝店"}
                ],
                "shopLevel": [
                    {"name": "5星级", "value": "5星级"},
                    {"name": "4星级", "value": "4星级"},
                    {"name": "3星级", "value": "3星级"},
                    {"name": "2星级", "value": "2星级"},
                    {"name": "1星级", "value": "1星级"},
                    {"name": "金冠级", "value": "1红冠,2红冠,3红冠,4红冠,5红冠"},
                    {"name": "皇冠级", "value": "1皇冠,2皇冠,3皇冠,4皇冠,5皇冠"},
                    {"name": "钻级", "value": "1钻,2钻,3钻,4钻,5钻"},
                    {"name": "心级", "value": "1心,2心,3心,4心,5心"}
                ],
                "icTags": [
                    {"name": "直播严选", "value": "2156610,2205634"},
                    {"name": "天猫超市", "value": "2388418"},
                    {"name": "源头优选", "value": "2243138"},
                    {"name": "淘工厂", "value": "1362498"},
                    {"name": "天天热卖", "value": "2758466,3554370"},
                    {"name": "天猫U先", "value": "271938"},
                    {"name": "淘宝秒杀", "value": "1792066"},
                    {"name": "淘宝买菜", "value": "3117442"}
                ]
            };
            renderConditions();
        } catch (error) {
            console.error('加载筛选条件失败:', error);
            addLog('加载筛选条件失败: ' + error.message, 'error');
        }
    }

    // 加载主播列表
    async function loadAnchors() {
        try {
            const response = await fetch('/api/product-collection/anchors', {
                headers: {
                    'X-API-Key': localStorage.getItem('apiKey') || ''
                }
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            if (result.success) {
                renderAnchors(result.data);
            } else {
                throw new Error(result.error || '获取主播列表失败');
            }
        } catch (error) {
            console.error('加载主播列表失败:', error);
            addLog('加载主播列表失败: ' + error.message, 'error');
        }
    }

    // 渲染筛选条件
    function renderConditions() {
        if (!conditionData) return;

        // 渲染商品类目
        if (conditionData.cateIds) {
            renderFilterButtons('categoryFilters', conditionData.cateIds, 'cateIds');
        }

        // 渲染商品标签
        if (conditionData.featured) {
            renderFilterButtons('featuredFilters', conditionData.featured, 'featured');
        }

        // 渲染商品价格
        if (conditionData.priceSelect) {
            renderFilterButtons('priceFilters', conditionData.priceSelect, 'priceSelect');
        }

        // 渲染佣金比例
        if (conditionData.commissionRateSelect) {
            renderFilterButtons('commissionFilters', conditionData.commissionRateSelect, 'commissionRateSelect');
        }

        // 渲染365天销量
        if (conditionData.soldQuantity365Select) {
            renderFilterButtons('salesFilters', conditionData.soldQuantity365Select, 'soldQuantity365Select');
        }

        // 渲染店铺类型
        if (conditionData.shopType) {
            renderFilterButtons('shopTypeFilters', conditionData.shopType, 'shopType');
        }

        // 渲染店铺等级
        if (conditionData.shopLevel) {
            renderFilterButtons('shopLevelFilters', conditionData.shopLevel, 'shopLevel');
        }

        // 渲染商品渠道
        if (conditionData.icTags) {
            renderFilterButtons('icTagsFilters', conditionData.icTags, 'icTags');
        }
    }

    // 渲染筛选按钮
    function renderFilterButtons(containerId, options, filterType) {
        const container = document.getElementById(containerId);
        container.innerHTML = '';

        if (Array.isArray(options)) {
            options.forEach(option => {
                const button = createFilterButton(option.name || option.label || option, option.value || option, filterType);
                container.appendChild(button);
            });
        } else if (typeof options === 'object') {
            Object.entries(options).forEach(([key, value]) => {
                const button = createFilterButton(value.name || value.label || key, value.value || key, filterType);
                container.appendChild(button);
            });
        }
    }

    // 创建筛选按钮
    function createFilterButton(text, value, filterType) {
        const button = document.createElement('button');
        button.className = 'filter-button';
        button.textContent = text;
        button.dataset.value = value;
        button.dataset.filterType = filterType;
        
        button.addEventListener('click', function() {
            // 对于某些筛选类型，只允许单选
            if (['priceSelect', 'commissionRateSelect', 'soldQuantity365Select'].includes(filterType)) {
                // 清除同类型的其他选中状态
                const siblings = this.parentElement.querySelectorAll('.filter-button');
                siblings.forEach(sibling => sibling.classList.remove('active'));
            }
            
            this.classList.toggle('active');
        });
        
        return button;
    }

    // 渲染主播列表
    function renderAnchors(anchors) {
        const container = document.getElementById('anchorButtons');
        container.innerHTML = '';

        anchors.forEach(anchor => {
            const button = document.createElement('button');
            button.className = 'anchor-button';
            button.textContent = anchor.anchor_name;
            button.dataset.anchorId = anchor.anchor_id;

            button.addEventListener('click', function() {
                // 清除其他主播的选中状态
                const siblings = this.parentElement.querySelectorAll('.anchor-button');
                siblings.forEach(sibling => sibling.classList.remove('active'));

                // 设置当前主播为选中状态
                this.classList.add('active');
                selectedAnchor = anchor;
                addLog(`已选择主播: ${anchor.anchor_name}`, 'info');
            });

            container.appendChild(button);
        });
    }

    // 绑定事件
    function bindEvents() {
        // 开始采集按钮
        document.getElementById('startCollectionBtn').addEventListener('click', startCollection);
        
        // 停止采集按钮
        document.getElementById('stopCollectionBtn').addEventListener('click', stopCollection);
        
        // 清空日志按钮
        document.getElementById('clearLogBtn').addEventListener('click', clearLog);
    }

    // 开始采集
    async function startCollection() {
        if (isCollecting) return;

        if (!selectedAnchor) {
            layer.msg('请先选择主播', {icon: 2});
            return;
        }

        const filters = getSelectedFilters();
        const interval = parseInt(document.getElementById('intervalInput').value) * 1000;
        const pageSize = parseInt(document.getElementById('pageSizeSelect').value);

        if (interval < 1000) {
            layer.msg('请求间隔不能少于1秒', {icon: 2});
            return;
        }

        isCollecting = true;
        currentPageNum = 1;
        collectedProducts = [];

        // 清空表格
        const tbody = document.getElementById('productTableBody');
        tbody.innerHTML = '';

        updateCollectionStatus('采集中...', true);
        addLog('开始采集商品数据...', 'info');
        addLog(`主播: ${selectedAnchor.anchor_name}`, 'info');
        addLog(`筛选条件: ${JSON.stringify(filters)}`, 'info');
        addLog(`请求间隔: ${interval/1000}秒`, 'info');

        // 立即执行第一次采集
        await collectPage(filters, pageSize);

        // 设置定时采集
        collectionInterval = setInterval(async () => {
            if (isCollecting) {
                await collectPage(filters, pageSize);
            }
        }, interval);
    }

    // 停止采集
    function stopCollection() {
        if (!isCollecting) return;

        isCollecting = false;
        if (collectionInterval) {
            clearInterval(collectionInterval);
            collectionInterval = null;
        }

        updateCollectionStatus('已停止', false);
        addLog('采集已停止', 'info');
        addLog(`总共采集了 ${collectedProducts.length} 个商品`, 'info');

        // 如果没有采集到数据，显示提示信息
        if (collectedProducts.length === 0) {
            const tbody = document.getElementById('productTableBody');
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center text-gray-500 py-8">
                        <i class="fas fa-inbox text-4xl mb-2 block"></i>
                        暂无数据，请开始采集
                    </td>
                </tr>
            `;
        }
    }

    // 采集单页数据
    async function collectPage(filters, pageSize) {
        try {
            addLog(`正在采集第 ${currentPageNum} 页...`, 'info');

            const response = await fetch('/api/product-collection/collect', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-API-Key': localStorage.getItem('apiKey') || ''
                },
                body: JSON.stringify({
                    anchorId: selectedAnchor.anchor_id,
                    filters: filters,
                    pageNum: currentPageNum,
                    pageSize: pageSize
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            
            if (result.success) {
                const products = result.data || [];
                collectedProducts = collectedProducts.concat(products);
                
                addLog(`第 ${currentPageNum} 页采集成功，获得 ${products.length} 个商品`, 'success');
                
                // 显示商品信息
                products.forEach(product => {
                    addLog(`商品: ${product.itemId} | ${product.itemName} | ¥${product.itemPrice} | 佣金:${product.commission}`, 'success');
                });

                // 更新表格显示
                updateProductTable(products);
                
                // 更新统计信息
                document.getElementById('currentPage').textContent = currentPageNum;
                document.getElementById('collectedCount').textContent = collectedProducts.length;

                // 如果还有更多数据，继续下一页
                if (!result.end && products.length > 0) {
                    currentPageNum++;
                } else {
                    addLog('所有页面采集完成', 'info');
                    stopCollection();
                }
            } else {
                throw new Error(result.error || '采集失败');
            }

        } catch (error) {
            console.error('采集失败:', error);
            addLog(`第 ${currentPageNum} 页采集失败: ${error.message}`, 'error');
            
            // 如果是认证错误，停止采集
            if (error.message.includes('401') || error.message.includes('Cookie')) {
                stopCollection();
            }
        }
    }

    // 获取选中的筛选条件
    function getSelectedFilters() {
        const filters = {};
        
        // 获取各种筛选条件
        const activeButtons = document.querySelectorAll('.filter-button.active');
        
        activeButtons.forEach(button => {
            const filterType = button.dataset.filterType;
            const value = button.dataset.value;
            
            if (filterType === 'cateIds' || filterType === 'featured' || filterType === 'icTags') {
                // 多选类型，用逗号分隔
                if (!filters[filterType]) {
                    filters[filterType] = [];
                }
                filters[filterType].push(value);
            } else {
                // 单选类型
                filters[filterType] = value;
            }
        });

        // 处理多选类型的值
        if (filters.cateIds) {
            filters.cateIds = filters.cateIds.join(',');
        }
        if (filters.featured) {
            filters.featured = filters.featured.join(',');
        }
        if (filters.icTags) {
            filters.icTags = filters.icTags.join(',');
        }

        return filters;
    }

    // 更新采集状态
    function updateCollectionStatus(status, isActive) {
        document.getElementById('collectionStatus').textContent = status;
        document.getElementById('startCollectionBtn').disabled = isActive;
        document.getElementById('stopCollectionBtn').disabled = !isActive;
    }

    // 添加日志
    function addLog(message, type = 'info') {
        const logContainer = document.getElementById('logContainer');
        const logEntry = document.createElement('div');
        logEntry.className = `log-entry log-${type}`;
        
        const timestamp = new Date().toLocaleTimeString();
        logEntry.textContent = `[${timestamp}] ${message}`;
        
        logContainer.appendChild(logEntry);
        logContainer.scrollTop = logContainer.scrollHeight;
    }

    // 清空日志
    function clearLog() {
        document.getElementById('logContainer').innerHTML = '<div class="log-entry log-info">日志已清空</div>';
    }

    // 更新商品表格
    function updateProductTable(products) {
        const tbody = document.getElementById('productTableBody');

        // 如果是第一次添加数据，清除"暂无数据"提示
        if (collectedProducts.length === products.length) {
            tbody.innerHTML = '';
        }

        products.forEach(product => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${product.itemId}</td>
                <td title="${product.itemName}">${truncateText(product.itemName, 30)}</td>
                <td>¥${product.itemPrice}</td>
                <td>${product.commission}</td>
                <td>${product.soldQuantity30}</td>
                <td>${product.soldQuantity365}</td>
                <td>${product.shopName}</td>
            `;
            tbody.appendChild(row);
        });
    }

    // 截断文本
    function truncateText(text, maxLength) {
        if (!text) return '';
        return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
    }
});
