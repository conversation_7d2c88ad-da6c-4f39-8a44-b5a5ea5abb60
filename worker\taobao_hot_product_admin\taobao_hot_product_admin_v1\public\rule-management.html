<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>规则管理 - 淘宝产品数据管理</title>

    <script src="https://registry.npmmirror.com/tailwindcss-cdn/3.4.10/files/tailwindcss.js"></script>
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://fast-1303094100.cos.ap-shanghai.myqcloud.com/static_file/js/jquery-3.5.0.min.js"></script>
    <script src="https://fast-1303094100.cos.ap-shanghai.myqcloud.com/static_file/js/layer.3.5.1/layer.js"></script>
    <style>
        .table-container {
            overflow-x: auto;
        }

        @media (max-width: 640px) {
            .pagination-desktop {
                display: none;
            }

            .pagination-mobile {
                display: flex;
            }
        }

        @media (min-width: 641px) {
            .pagination-desktop {
                display: flex;
            }

            .pagination-mobile {
                display: none;
            }
        }

        .stat-card {
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        .content-width {
            width: 80%;
        }

        .filter-btn {
            transition: all 0.2s ease;
        }

        .filter-btn.selected {
            background-color: #f97316;
            border-color: #f97316;
            color: white;
        }

        .filter-btn.selected:hover {
            background-color: #ea580c;
            border-color: #ea580c;
        }

        .filter-all-btn.selected {
            background-color: #2563eb;
            border-color: #2563eb;
            color: white;
        }

        .filter-all-btn.selected:hover {
            background-color: #1d4ed8;
            border-color: #1d4ed8;
        }

        .filter-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .action-btn {
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
            border-radius: 0.375rem;
            transition: all 0.2s ease;
        }

        .action-btn:hover {
            transform: translateY(-1px);
        }

        .btn-detail {
            background-color: #3b82f6;
            color: white;
        }

        .btn-detail:hover {
            background-color: #2563eb;
        }

        .btn-delete {
            background-color: #ef4444;
            color: white;
        }

        .btn-delete:hover {
            background-color: #dc2626;
        }

        /* Modal styles */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .modal-content {
            background: white;
            border-radius: 16px;
            max-width: 800px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        .modal-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .modal-body {
            padding: 1.5rem;
        }

        .modal-footer {
            padding: 1.5rem;
            border-top: 1px solid #e2e8f0;
            display: flex;
            justify-content: flex-end;
            gap: 0.75rem;
        }

        .detail-item {
            margin-bottom: 1rem;
            padding: 0.75rem;
            background: #f8fafc;
            border-radius: 8px;
        }

        .detail-label {
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.25rem;
        }

        .detail-value {
            color: #6b7280;
        }

        .json-display {
            background: #1f2937;
            color: #f9fafb;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>

<body class="bg-gray-50">
    <!-- 导航栏 -->
    <nav class="bg-orange-600 shadow">
        <div class="content-width mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex">
                    <div class="flex-shrink-0 flex items-center">
                        <span class="text-xl font-bold text-white">规则管理</span>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="products.html" class="text-white hover:text-orange-200 px-3 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-box mr-1"></i>产品管理
                    </a>
                    <a href="selection-rules.html" class="text-white hover:text-orange-200 px-3 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-filter mr-1"></i>选品规则
                    </a>
                    <a href="rule-management.html" class="bg-orange-700 text-white px-3 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-cogs mr-1"></i>规则管理
                    </a>
                    <a href="anchors.html" class="text-white hover:text-orange-200 px-3 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-microphone mr-1"></i>主播管理
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主内容区 -->
    <main class="content-width mx-auto py-6 sm:px-6 lg:px-8">
        <!-- 筛选表单 -->
        <div class="bg-white shadow rounded-lg mb-6 p-4">
            <form id="filterForm" class="grid grid-cols-1 gap-y-4 gap-x-4 sm:grid-cols-6">
                <!-- 规则名称搜索 -->
                <div class="sm:col-span-2">
                    <label for="ruleNameFilter" class="block text-sm font-medium text-gray-700">规则名称</label>
                    <div class="mt-1">
                        <input type="text" id="ruleNameFilter" name="ruleName" placeholder="输入规则名称搜索"
                            class="shadow-sm focus:ring-orange-500 focus:border-orange-500 block w-full sm:text-sm border-gray-300 rounded-md p-2 border">
                    </div>
                </div>



                <!-- 状态筛选 -->
                <div class="sm:col-span-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">状态</label>
                    <div class="flex flex-wrap gap-2">
                        <button type="button" class="filter-all-btn px-3 py-1 text-sm border border-blue-300 rounded-md bg-blue-50 text-blue-700 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors font-medium selected">
                            全部
                        </button>
                        <button type="button" data-value="active" class="filter-btn px-3 py-1 text-sm border border-gray-300 rounded-md bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors">
                            活跃
                        </button>
                        <button type="button" data-value="inactive" class="filter-btn px-3 py-1 text-sm border border-gray-300 rounded-md bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors">
                            已删除
                        </button>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="sm:col-span-6 flex justify-between items-center">
                    <div class="flex items-center">
                        <button type="submit"
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500">
                            <i class="fas fa-search mr-2"></i> 查询
                        </button>
                        <button type="button" id="resetBtn"
                            class="ml-3 inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500">
                            <i class="fas fa-redo mr-2"></i> 重置
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- 数据概览 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div class="bg-white rounded-lg shadow p-4 stat-card">
                <div class="flex items-center">
                    <div class="w-12 h-12 rounded-full bg-blue-100 mr-4 flex items-center justify-center">
                        <i class="fas fa-cogs text-blue-500"></i>
                    </div>
                    <div>
                        <p class="text-gray-500 text-sm">总规则数</p>
                        <p class="text-2xl font-semibold text-gray-700" id="totalRules">--</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-4 stat-card">
                <div class="flex items-center">
                    <div class="w-12 h-12 rounded-full bg-green-100 mr-4 flex items-center justify-center">
                        <i class="fas fa-check-circle text-green-500"></i>
                    </div>
                    <div>
                        <p class="text-gray-500 text-sm">活跃规则</p>
                        <p class="text-2xl font-semibold text-gray-700" id="activeRules">--</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-4 stat-card">
                <div class="flex items-center">
                    <div class="w-12 h-12 rounded-full bg-yellow-100 mr-4 flex items-center justify-center">
                        <i class="fas fa-chart-line text-yellow-500"></i>
                    </div>
                    <div>
                        <p class="text-gray-500 text-sm">平均目标数</p>
                        <p class="text-2xl font-semibold text-gray-700" id="avgTarget">--</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-4 stat-card">
                <div class="flex items-center">
                    <div class="w-12 h-12 rounded-full bg-purple-100 mr-4 flex items-center justify-center">
                        <i class="fas fa-calendar text-purple-500"></i>
                    </div>
                    <div>
                        <p class="text-gray-500 text-sm">今日创建</p>
                        <p class="text-2xl font-semibold text-gray-700" id="todayCreated">--</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据表格 -->
        <div class="bg-white shadow overflow-hidden rounded-lg">
            <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
                <h3 class="text-lg leading-6 font-medium text-gray-900">规则列表</h3>
                <div class="flex items-center">
                    <div class="text-sm text-gray-500 mr-4">共 <span id="totalCount">0</span> 条记录</div>
                </div>
            </div>
            <div class="border-t border-gray-200 table-container">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                规则ID
                            </th>
                            <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                规则名称
                            </th>
                            <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                描述
                            </th>
                            <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                目标数量
                            </th>
                            <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                状态
                            </th>
                            <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                创建时间
                            </th>
                            <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                操作
                            </th>
                        </tr>
                    </thead>
                    <tbody id="rulesTable" class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td colspan="6" class="px-6 py-4 text-center text-sm text-gray-500">加载中...</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <!-- 移动端分页 -->
                <div class="pagination-mobile flex-1 flex justify-between">
                    <button id="prevPageMobile"
                        class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        上一页
                    </button>
                    <div class="text-sm text-gray-700 flex items-center">
                        第 <span id="currentPageMobile" class="mx-1">1</span> / <span id="totalPagesMobile" class="mx-1">1</span> 页
                    </div>
                    <button id="nextPageMobile"
                        class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        下一页
                    </button>
                </div>
                <!-- 桌面端分页 -->
                <div class="pagination-desktop flex-1 sm:flex sm:items-center sm:justify-between ml-4">
                    <div>
                        <p class="text-sm text-gray-700">
                            显示第 <span id="startItem" class="font-medium">1</span> 到第
                            <span id="endItem" class="font-medium">10</span> 条，共
                            <span id="totalItems" class="font-medium">0</span> 条
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination" id="pagination">
                            <!-- 分页按钮将通过JavaScript填充 -->
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 规则详情模态框 -->
    <div id="ruleDetailModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="text-lg font-semibold text-gray-900">规则详情</h3>
            </div>
            <div class="modal-body">
                <div id="ruleDetailContent">
                    <!-- 规则详情内容将在这里动态生成 -->
                </div>
            </div>
            <div class="modal-footer">
                <button id="closeDetailBtn" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500">
                    关闭
                </button>
            </div>
        </div>
    </div>

    <!-- 访问密码输入弹窗 -->
    <div id="apiKeyModal" class="fixed z-10 inset-0 overflow-y-auto hidden">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                            <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                                请输入访问密码
                            </h3>
                            <div class="mt-4">
                                <form id="apiKeyForm">
                                    <div class="mb-4">
                                        <label for="apiKey" class="block text-sm font-medium text-gray-700">访问密码</label>
                                        <input type="password" name="apiKey" id="apiKey"
                                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                                            required>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button id="submitApiKeyBtn" type="button"
                        class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-orange-600 text-base font-medium text-white hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 sm:ml-3 sm:w-auto sm:text-sm">
                        确认
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 当前页码和每页显示数量
        let currentPage = 1;
        let pageSize = 10;
        let totalPages = 0;
        let filters = {};
        let searchTimeout = null; // 防抖定时器

        // 多选框选中的值
        let selectedStatus = [];

        // 页面加载完成后获取数据
        $(document).ready(function () {
            if (typeof layer === 'undefined') {
                console.error('layer.js 未正确加载');
                return;
            }

            initEventListeners();

            // 检查是否有访问密码，如果没有则显示输入弹窗
            const apiKey = getCookie('api_key');
            if (!apiKey) {
                showApiKeyModal();
                return;
            }

            // 初始化多选框状态
            updateSelectedStatus();

            // 加载数据
            loadRulesAndStats();
        });

        // 初始化事件监听器
        function initEventListeners() {
            // 筛选表单提交
            $('#filterForm').on('submit', function (e) {
                e.preventDefault();
                currentPage = 1;
                loadRules();
            });

            // 重置按钮
            $('#resetBtn').on('click', function () {
                $('#filterForm')[0].reset();
                // 重置多选按钮
                $('.filter-btn').removeClass('selected');
                $('.filter-all-btn').addClass('selected');
                selectedStatus = [];
                currentPage = 1;
                loadRules();
            });

            // 移动端分页按钮
            $('#prevPageMobile').on('click', function () {
                if (currentPage > 1) {
                    currentPage--;
                    loadRules();
                }
            });

            $('#nextPageMobile').on('click', function () {
                if (currentPage < totalPages) {
                    currentPage++;
                    loadRules();
                }
            });

            // 访问密码提交按钮
            $('#submitApiKeyBtn').on('click', function () {
                submitApiKey();
            });

            // 访问密码表单回车提交
            $('#apiKeyForm').on('submit', function (e) {
                e.preventDefault();
                submitApiKey();
            });

            // 筛选条件自动触发查询
            $('#ruleNameFilter').on('input', function () {
                debouncedSearch();
            });



            // 状态按钮事件
            $('.filter-btn').on('click', function () {
                $(this).toggleClass('selected');
                updateSelectedStatus();
                updateAllButtonState();
                debouncedSearch();
            });

            // 状态全部按钮事件
            $('.filter-all-btn').on('click', function () {
                toggleAllSelection();
            });

            // 关闭详情模态框
            $('#closeDetailBtn').on('click', function () {
                hideRuleDetailModal();
            });

            // 点击模态框外部关闭
            $('#ruleDetailModal').on('click', function (e) {
                if (e.target === this) {
                    hideRuleDetailModal();
                }
            });
        }

        // 更新选中的状态
        function updateSelectedStatus() {
            selectedStatus = [];
            $('.filter-btn.selected').each(function() {
                selectedStatus.push($(this).data('value'));
            });
        }

        // 切换全选状态
        function toggleAllSelection() {
            const allBtn = $('.filter-all-btn');
            const itemBtns = $('.filter-btn');

            if (allBtn.hasClass('selected')) {
                // 当前是全选状态，取消全选
                allBtn.removeClass('selected');
                itemBtns.removeClass('selected');
            } else {
                // 当前不是全选状态，执行全选
                allBtn.addClass('selected');
                itemBtns.addClass('selected');
            }

            updateSelectedStatus();
            debouncedSearch();
        }

        // 更新全部按钮状态
        function updateAllButtonState() {
            const allBtn = $('.filter-all-btn');
            const itemBtns = $('.filter-btn');
            const selectedItemBtns = $('.filter-btn.selected');

            // 如果所有单项都被选中，则全部按钮也应该被选中
            if (selectedItemBtns.length === itemBtns.length && itemBtns.length > 0) {
                allBtn.addClass('selected');
            } else {
                allBtn.removeClass('selected');
            }
        }

        // 防抖查询函数
        function debouncedSearch() {
            // 清除之前的定时器
            if (searchTimeout) {
                clearTimeout(searchTimeout);
            }

            // 设置新的定时器，300ms后执行查询
            searchTimeout = setTimeout(function () {
                currentPage = 1;
                loadRules();
            }, 300);
        }

        // 加载规则和统计数据
        async function loadRulesAndStats() {
            await Promise.all([
                loadRules(),
                loadStats()
            ]);
        }

        // 加载规则列表
        async function loadRules() {
            try {
                // 构建查询参数
                const params = new URLSearchParams({
                    page: currentPage,
                    limit: pageSize
                });

                // 添加筛选条件
                const ruleName = $('#ruleNameFilter').val().trim();
                if (ruleName) {
                    params.append('ruleName', ruleName);
                }



                if (selectedStatus.length > 0) {
                    selectedStatus.forEach(status => {
                        params.append('status', status);
                    });
                }

                const response = await fetch(`/api/selection-rules?${params}`, {
                    headers: addApiKeyHeader()
                });

                if (response.status === 401) {
                    const data = await response.json();
                    if (data.error === "invalid_api_key") {
                        showApiKeyModal();
                        return;
                    }
                }

                const data = await response.json();

                if (data.success) {
                    displayRules(data.data || []);
                    if (data.pagination) {
                        updatePagination(data.pagination);
                    } else {
                        // 兼容旧版本API响应
                        updatePagination({ total: data.data?.length || 0, page: currentPage, limit: pageSize });
                    }
                } else {
                    console.error('加载规则失败:', data.message);
                    $('#rulesTable').html('<tr><td colspan="9" class="px-6 py-4 text-center text-sm text-red-500">加载失败: ' + data.message + '</td></tr>');
                }
            } catch (error) {
                console.error('加载规则失败:', error);
                $('#rulesTable').html('<tr><td colspan="9" class="px-6 py-4 text-center text-sm text-red-500">网络错误，请稍后重试</td></tr>');
            }
        }

        // 显示规则列表
        function displayRules(rules) {
            const tbody = $('#rulesTable');

            if (rules.length === 0) {
                tbody.html('<tr><td colspan="6" class="px-6 py-4 text-center text-sm text-gray-500">暂无数据</td></tr>');
                return;
            }

            const rows = rules.map(rule => {
                const statusBadge = rule.status === 'active'
                    ? '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">活跃</span>'
                    : '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">已删除</span>';

                const createdAt = new Date(rule.created_at).toLocaleString('zh-CN');

                return `
                    <tr class="hover:bg-gray-50">
                        <td class="px-3 py-4 whitespace-nowrap text-sm text-center text-gray-900">${rule.id}</td>
                        <td class="px-3 py-4 whitespace-nowrap text-sm text-center text-gray-900 font-medium">${rule.rule_name || '未命名'}</td>
                        <td class="px-3 py-4 text-sm text-center text-gray-500 max-w-xs truncate" title="${rule.description || ''}">${rule.description || '无描述'}</td>
                        <td class="px-3 py-4 whitespace-nowrap text-sm text-center text-gray-900">${rule.total_target || 0}</td>
                        <td class="px-3 py-4 whitespace-nowrap text-sm text-center">${statusBadge}</td>
                        <td class="px-3 py-4 whitespace-nowrap text-sm text-center text-gray-500">${createdAt}</td>
                        <td class="px-3 py-4 whitespace-nowrap text-sm text-center">
                            <div class="flex justify-center space-x-2">
                                <button onclick="showRuleDetail(${rule.id})" class="action-btn btn-detail">
                                    <i class="fas fa-eye mr-1"></i>详情
                                </button>
                                <button onclick="deleteRule(${rule.id})" class="action-btn btn-delete">
                                    <i class="fas fa-trash mr-1"></i>删除
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            }).join('');

            tbody.html(rows);
        }

        // 加载统计数据
        async function loadStats() {
            try {
                const response = await fetch('/api/selection-rules', {
                    headers: addApiKeyHeader()
                });

                if (response.status === 401) {
                    return;
                }

                const data = await response.json();

                if (data.success) {
                    const rules = data.data || [];
                    const totalRules = rules.length;
                    const activeRules = rules.filter(rule => rule.status === 'active').length;
                    const avgTarget = rules.length > 0 ? Math.round(rules.reduce((sum, rule) => sum + (rule.total_target || 0), 0) / rules.length) : 0;

                    // 计算今日创建的规则数
                    const today = new Date().toDateString();
                    const todayCreated = rules.filter(rule => {
                        const ruleDate = new Date(rule.created_at).toDateString();
                        return ruleDate === today;
                    }).length;

                    $('#totalRules').text(totalRules);
                    $('#activeRules').text(activeRules);
                    $('#avgTarget').text(avgTarget);
                    $('#todayCreated').text(todayCreated);
                }
            } catch (error) {
                console.error('加载统计数据失败:', error);
            }
        }



        // 显示规则详情
        async function showRuleDetail(ruleId) {
            try {
                const response = await fetch(`/api/selection-rules/${ruleId}`, {
                    headers: addApiKeyHeader()
                });

                if (response.status === 401) {
                    showApiKeyModal();
                    return;
                }

                const data = await response.json();

                if (data.success) {
                    displayRuleDetail(data.data);
                    showRuleDetailModal();
                } else {
                    layer.msg('获取规则详情失败: ' + data.message, { icon: 2 });
                }
            } catch (error) {
                console.error('获取规则详情失败:', error);
                layer.msg('网络错误，请稍后重试', { icon: 2 });
            }
        }

        // 显示规则详情内容
        function displayRuleDetail(rule) {
            const content = $('#ruleDetailContent');

            let ruleGroupsDisplay = '无';
            if (rule.rule_groups) {
                try {
                    const groups = typeof rule.rule_groups === 'string' ? JSON.parse(rule.rule_groups) : rule.rule_groups;
                    ruleGroupsDisplay = `<div class="json-display">${JSON.stringify(groups, null, 2)}</div>`;
                } catch (e) {
                    ruleGroupsDisplay = rule.rule_groups;
                }
            }

            let comprehensiveConfigDisplay = '无';
            if (rule.comprehensive_config) {
                try {
                    const config = typeof rule.comprehensive_config === 'string' ? JSON.parse(rule.comprehensive_config) : rule.comprehensive_config;
                    comprehensiveConfigDisplay = `<div class="json-display">${JSON.stringify(config, null, 2)}</div>`;
                } catch (e) {
                    comprehensiveConfigDisplay = rule.comprehensive_config;
                }
            }

            const statusBadge = rule.status === 'active'
                ? '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">活跃</span>'
                : '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">已删除</span>';

            const createdAt = new Date(rule.created_at).toLocaleString('zh-CN');
            const updatedAt = new Date(rule.updated_at).toLocaleString('zh-CN');

            content.html(`
                <div class="detail-item">
                    <div class="detail-label">规则ID</div>
                    <div class="detail-value">${rule.id}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">规则名称</div>
                    <div class="detail-value">${rule.rule_name || '未命名'}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">描述</div>
                    <div class="detail-value">${rule.description || '无描述'}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">目标数量</div>
                    <div class="detail-value">${rule.total_target || 0}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">状态</div>
                    <div class="detail-value">${statusBadge}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">创建时间</div>
                    <div class="detail-value">${createdAt}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">更新时间</div>
                    <div class="detail-value">${updatedAt}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">规则组配置</div>
                    <div class="detail-value">${ruleGroupsDisplay}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">综合配置</div>
                    <div class="detail-value">${comprehensiveConfigDisplay}</div>
                </div>
            `);
        }

        // 删除规则
        async function deleteRule(ruleId) {
            layer.confirm('确定要删除这个规则吗？删除后无法恢复。', {
                icon: 3,
                title: '确认删除'
            }, async (index) => {
                try {
                    const response = await fetch(`/api/selection-rules/${ruleId}`, {
                        method: 'DELETE',
                        headers: addApiKeyHeader()
                    });

                    if (response.status === 401) {
                        showApiKeyModal();
                        return;
                    }

                    const data = await response.json();

                    if (data.success) {
                        layer.msg('删除成功', { icon: 1 });
                        loadRulesAndStats(); // 重新加载数据
                    } else {
                        layer.msg('删除失败: ' + data.message, { icon: 2 });
                    }
                } catch (error) {
                    console.error('删除失败:', error);
                    layer.msg('网络错误，请稍后重试', { icon: 2 });
                }
                layer.close(index);
            });
        }

        // 显示规则详情模态框
        function showRuleDetailModal() {
            $('#ruleDetailModal').removeClass('hidden');
        }

        // 隐藏规则详情模态框
        function hideRuleDetailModal() {
            $('#ruleDetailModal').addClass('hidden');
        }

        // 更新分页信息
        function updatePagination(pagination) {
            const { total, page, limit } = pagination;
            totalPages = Math.ceil(total / limit);
            currentPage = page;

            // 更新总数显示
            $('#totalCount').text(total);

            // 更新移动端分页
            $('#currentPageMobile').text(currentPage);
            $('#totalPagesMobile').text(totalPages);

            // 更新桌面端分页信息
            const startItem = total === 0 ? 0 : (currentPage - 1) * limit + 1;
            const endItem = Math.min(currentPage * limit, total);
            $('#startItem').text(startItem);
            $('#endItem').text(endItem);
            $('#totalItems').text(total);

            // 更新分页按钮状态
            $('#prevPageMobile').prop('disabled', currentPage <= 1);
            $('#nextPageMobile').prop('disabled', currentPage >= totalPages);

            // 生成桌面端分页按钮
            generatePaginationButtons();
        }

        // 生成分页按钮
        function generatePaginationButtons() {
            const pagination = $('#pagination');
            pagination.empty();

            if (totalPages <= 1) return;

            // 上一页按钮
            const prevDisabled = currentPage <= 1;
            pagination.append(`
                <button ${prevDisabled ? 'disabled' : ''} onclick="changePage(${currentPage - 1})"
                    class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 ${prevDisabled ? 'cursor-not-allowed opacity-50' : ''}">
                    <i class="fas fa-chevron-left"></i>
                </button>
            `);

            // 页码按钮
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            if (startPage > 1) {
                pagination.append(`
                    <button onclick="changePage(1)" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">1</button>
                `);
                if (startPage > 2) {
                    pagination.append(`<span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">...</span>`);
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                const isActive = i === currentPage;
                pagination.append(`
                    <button onclick="changePage(${i})" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium ${isActive ? 'bg-orange-50 border-orange-500 text-orange-600' : 'bg-white text-gray-700 hover:bg-gray-50'}">${i}</button>
                `);
            }

            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    pagination.append(`<span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">...</span>`);
                }
                pagination.append(`
                    <button onclick="changePage(${totalPages})" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">${totalPages}</button>
                `);
            }

            // 下一页按钮
            const nextDisabled = currentPage >= totalPages;
            pagination.append(`
                <button ${nextDisabled ? 'disabled' : ''} onclick="changePage(${currentPage + 1})"
                    class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 ${nextDisabled ? 'cursor-not-allowed opacity-50' : ''}">
                    <i class="fas fa-chevron-right"></i>
                </button>
            `);
        }

        // 切换页面
        function changePage(page) {
            if (page >= 1 && page <= totalPages && page !== currentPage) {
                currentPage = page;
                loadRules();
            }
        }

        // 显示访问密码输入弹窗
        function showApiKeyModal() {
            $('#apiKeyModal').removeClass('hidden');
        }

        // 隐藏访问密码输入弹窗
        function hideApiKeyModal() {
            $('#apiKeyModal').addClass('hidden');
        }

        // 提交访问密码
        async function submitApiKey() {
            const apiKey = $('#apiKey').val().trim();
            if (!apiKey) {
                layer.msg('请输入访问密码', { icon: 2 });
                return;
            }

            try {
                const response = await fetch('/api/anchors?mode=simple', {
                    headers: {
                        'X-API-Key': apiKey
                    }
                });

                if (response.ok) {
                    // 保存API密钥到cookie
                    setCookie('api_key', apiKey, 7); // 保存7天
                    hideApiKeyModal();
                    layer.msg('验证成功', { icon: 1 });

                    // 重新加载数据
                    loadRulesAndStats();
                } else {
                    layer.msg('访问密码错误', { icon: 2 });
                }
            } catch (error) {
                console.error('验证失败:', error);
                layer.msg('验证失败，请检查网络连接', { icon: 2 });
            }
        }

        // 添加API密钥到请求头
        function addApiKeyHeader() {
            const apiKey = getCookie('api_key');
            return apiKey ? { 'X-API-Key': apiKey } : {};
        }

        // 设置Cookie
        function setCookie(name, value, days) {
            const expires = new Date();
            expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
            document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/`;
        }

        // 获取Cookie
        function getCookie(name) {
            const nameEQ = name + "=";
            const ca = document.cookie.split(';');
            for (let i = 0; i < ca.length; i++) {
                let c = ca[i];
                while (c.charAt(0) === ' ') c = c.substring(1, c.length);
                if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
            }
            return null;
        }
    </script>
</body>

</html>
