<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品采集 - 淘宝热销产品管理系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/layer@3.5.1/dist/layer.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .filter-button {
            @apply px-3 py-1 m-1 text-sm border border-gray-300 rounded cursor-pointer transition-colors;
        }
        .filter-button:hover {
            @apply bg-gray-100;
        }
        .filter-button.active {
            @apply bg-blue-500 text-white border-blue-500;
        }
        .anchor-button {
            @apply px-4 py-2 m-1 text-sm border border-gray-300 rounded cursor-pointer transition-colors;
        }
        .anchor-button:hover {
            @apply bg-gray-100;
        }
        .anchor-button.active {
            @apply bg-green-500 text-white border-green-500;
        }
        .log-container {
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .log-entry {
            padding: 2px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .log-success {
            color: #10b981;
        }
        .log-error {
            color: #ef4444;
        }
        .log-info {
            color: #3b82f6;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- 导航栏 -->
    <nav class="bg-orange-600 shadow">
        <div class="content-width mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex">
                    <div class="flex-shrink-0 flex items-center">
                        <span class="text-xl font-bold text-white">淘宝产品数据管理 - 商品采集</span>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="products.html" class="text-white hover:text-orange-200 px-3 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-box mr-1"></i>产品管理
                    </a>
                    <a href="selection-rules.html" class="text-white hover:text-orange-200 px-3 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-filter mr-1"></i>选品规则
                    </a>
                    <a href="rule-management.html" class="text-white hover:text-orange-200 px-3 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-cogs mr-1"></i>规则管理
                    </a>
                    <a href="anchors.html" class="text-white hover:text-orange-200 px-3 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-microphone mr-1"></i>主播管理
                    </a>
                    <a href="product-collection.html" class="bg-orange-700 text-white px-3 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-shopping-cart mr-1"></i>商品采集
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mx-auto px-4 py-8">
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="mb-6">
                <h1 class="text-2xl font-bold text-gray-800">
                    <i class="fas fa-shopping-cart mr-2"></i>商品采集
                </h1>
                <p class="text-gray-600 mt-2">根据筛选条件采集淘宝商品数据</p>
            </div>

            <!-- 主播选择 -->
            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-3">选择主播</h3>
                <div id="anchorButtons" class="flex flex-wrap">
                    <!-- 主播按钮将在这里动态生成 -->
                </div>
            </div>

            <!-- 筛选条件 -->
            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-3">筛选条件</h3>
                
                <!-- 商品类目 -->
                <div class="mb-4">
                    <h4 class="font-medium mb-2">商品类目</h4>
                    <div id="categoryFilters" class="flex flex-wrap">
                        <!-- 类目按钮将在这里动态生成 -->
                    </div>
                </div>

                <!-- 商品标签 -->
                <div class="mb-4">
                    <h4 class="font-medium mb-2">商品标签</h4>
                    <div id="featuredFilters" class="flex flex-wrap">
                        <!-- 标签按钮将在这里动态生成 -->
                    </div>
                </div>

                <!-- 商品价格 -->
                <div class="mb-4">
                    <h4 class="font-medium mb-2">商品价格</h4>
                    <div id="priceFilters" class="flex flex-wrap">
                        <!-- 价格按钮将在这里动态生成 -->
                    </div>
                </div>

                <!-- 佣金比例 -->
                <div class="mb-4">
                    <h4 class="font-medium mb-2">佣金比例</h4>
                    <div id="commissionFilters" class="flex flex-wrap">
                        <!-- 佣金按钮将在这里动态生成 -->
                    </div>
                </div>

                <!-- 365天销量 -->
                <div class="mb-4">
                    <h4 class="font-medium mb-2">365天销量</h4>
                    <div id="salesFilters" class="flex flex-wrap">
                        <!-- 销量按钮将在这里动态生成 -->
                    </div>
                </div>

                <!-- 店铺类型 -->
                <div class="mb-4">
                    <h4 class="font-medium mb-2">店铺类型</h4>
                    <div id="shopTypeFilters" class="flex flex-wrap">
                        <!-- 店铺类型按钮将在这里动态生成 -->
                    </div>
                </div>

                <!-- 店铺等级 -->
                <div class="mb-4">
                    <h4 class="font-medium mb-2">店铺等级</h4>
                    <div id="shopLevelFilters" class="flex flex-wrap">
                        <!-- 店铺等级按钮将在这里动态生成 -->
                    </div>
                </div>

                <!-- 商品渠道 -->
                <div class="mb-4">
                    <h4 class="font-medium mb-2">商品渠道</h4>
                    <div id="icTagsFilters" class="flex flex-wrap">
                        <!-- 商品渠道按钮将在这里动态生成 -->
                    </div>
                </div>
            </div>

            <!-- 采集控制 -->
            <div class="mb-6 p-4 bg-gray-50 rounded-lg">
                <div class="flex items-center space-x-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">请求间隔（秒）</label>
                        <input type="number" id="intervalInput" value="3" min="1" max="60" 
                               class="w-20 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">每页数量</label>
                        <select id="pageSizeSelect" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="30">30</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                    </div>
                    <div class="flex-1">
                        <label class="block text-sm font-medium text-gray-700 mb-1">操作</label>
                        <div class="flex space-x-2">
                            <button id="startCollectionBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                                <i class="fas fa-play mr-2"></i>开始采集
                            </button>
                            <button id="stopCollectionBtn" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded" disabled>
                                <i class="fas fa-stop mr-2"></i>停止采集
                            </button>
                            <button id="clearLogBtn" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded">
                                <i class="fas fa-trash mr-2"></i>清空日志
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 采集状态 -->
                <div class="flex items-center space-x-4 text-sm">
                    <span>状态: <span id="collectionStatus" class="font-medium text-gray-600">未开始</span></span>
                    <span>当前页: <span id="currentPage" class="font-medium">0</span></span>
                    <span>已采集: <span id="collectedCount" class="font-medium">0</span> 个商品</span>
                </div>
            </div>

            <!-- 日志显示 -->
            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-3">采集日志</h3>
                <div id="logContainer" class="log-container bg-gray-900 text-green-400 p-4 rounded-lg">
                    <div class="log-entry log-info">等待开始采集...</div>
                </div>
            </div>

            <!-- 商品预览 -->
            <div>
                <h3 class="text-lg font-semibold mb-3">商品预览</h3>
                <div class="overflow-x-auto">
                    <table class="min-w-full bg-white border border-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">商品ID</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">商品标题</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">价格</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">佣金</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">30天销量</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">365天销量</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">店铺名称</th>
                            </tr>
                        </thead>
                        <tbody id="productTableBody" class="bg-white divide-y divide-gray-200">
                            <!-- 商品数据将在这里动态生成 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="js/product-collection.js"></script>
</body>
</html>
